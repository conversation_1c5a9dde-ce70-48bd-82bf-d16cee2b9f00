{"version": "1.1.1", "name": "resolve.exports", "repository": "lukeed/resolve.exports", "description": "A tiny (813b), correct, general-purpose, and configurable \"exports\" resolver without file-system reliance", "module": "dist/index.mjs", "main": "dist/index.js", "types": "index.d.ts", "license": "MIT", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "engines": {"node": ">=10"}, "scripts": {"build": "bundt", "test": "uvu -r esm test"}, "files": ["*.d.ts", "dist"], "exports": {".": {"types": "./index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "keywords": ["esm", "exports", "<PERSON><PERSON><PERSON><PERSON>", "fields", "modules", "resolution", "resolve"], "devDependencies": {"bundt": "1.1.2", "esm": "3.2.25", "uvu": "0.5.1"}}